// Salin dari sini
import React from "react";

const CigiFarm = () => {
  return (
    <>
      {/* Gambar & Profil */}
      <div className="flex flex-col md:flex-row items-center gap-8 mb-12">
        <img
          src="https://picsum.photos/600/400"
          alt="Cigi Farm"
          className="w-full md:w-1/2 rounded-xl shadow-lg object-cover"
        />
        <div>
          <h2 className="text-2xl font-semibold mb-4 text-white">
            Tentang Cigi Farm
          </h2>
          <p className="text-gray-300">
            <strong>Cigi Farm</strong> adalah unit usaha masyarakat yang
            bergerak di bidang peternakan ayam petelur, serta budidaya ikan lele
            dan ikan nila. Terletak di Desa Cimande Girang, Cigi Farm hadir
            untuk menyediakan sumber pangan sehat, berkualitas, dan
            berkelanjutan bagi warga desa dan sekitarnya.
          </p>
        </div>
      </div>

      {/* <PERSON>jar<PERSON> */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 text-white">
          Sejarah Singkat
        </h2>
        <p className="text-gray-300 leading-relaxed">
          Cigi Farm didirikan pada tahun <strong>2021</strong> sebagai hasil
          gotong royong masyarakat Desa Cimande Girang. Awalnya dimulai dari
          beberapa kandang ayam dan kolam ikan kecil. Kini Cigi Farm berkembang
          menjadi salah satu sumber pangan utama desa dan membuka peluang kerja
          lokal.
        </p>
      </div>

      {/* Visi Misi */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 text-white">Visi & Misi</h2>
        <div className="text-gray-300 space-y-4">
          <div>
            <h3 className="font-semibold text-lg">Visi:</h3>
            <p>
              Menjadi pusat produksi pangan desa yang sehat, berkelanjutan, dan
              berdaya saing tinggi.
            </p>
          </div>
          <div>
            <h3 className="font-semibold text-lg">Misi:</h3>
            <ul className="list-disc list-inside space-y-2">
              <li>
                Meningkatkan produksi ternak dan perikanan secara efisien dan
                ramah lingkungan
              </li>
              <li>
                Memberdayakan masyarakat lokal melalui pelatihan dan lapangan
                kerja
              </li>
              <li>Menjamin ketersediaan pangan sehat bagi warga desa</li>
              <li>Membangun jaringan distribusi dan pemasaran yang luas</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Produksi dalam format infografis */}
      <div className="mb-20 text-center">
        <h2 className="text-2xl font-semibold mb-8 text-white">
          Capaian Produksi Cigi Farm
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
          <div>
            <p className="text-4xl font-bold text-orange-400">3+</p>
            <p className="text-gray-300 mt-1">Peti Telur / Hari</p>
          </div>
          <div>
            <p className="text-4xl font-bold text-orange-400">300+</p>
            <p className="text-gray-300 mt-1">Populasi Ayam</p>
          </div>
          <div>
            <p className="text-4xl font-bold text-orange-400">10+</p>
            <p className="text-gray-300 mt-1">Kolam Lele & Nila</p>
          </div>
          <div>
            <p className="text-4xl font-bold text-orange-400">700+</p>
            <p className="text-gray-300 mt-1">Populasi Ikan</p>
          </div>
          <div>
            <p className="text-4xl font-bold text-orange-400">100%</p>
            <p className="text-gray-300 mt-1">Organik & Ramah Lingkungan</p>
          </div>
        </div>
      </div>

      {/* Galeri */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 text-white">
          Galeri Kegiatan
        </h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <img
            src="https://picsum.photos/seed/ayam/300/200"
            alt="Ayam Petelur"
            className="rounded-lg shadow"
          />
          <img
            src="https://picsum.photos/seed/lele/300/200"
            alt="Kolam Lele"
            className="rounded-lg shadow"
          />
          <img
            src="https://picsum.photos/seed/nila/300/200"
            alt="Panen Ikan"
            className="rounded-lg shadow"
          />
        </div>
      </div>

      {/* Lokasi */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 text-white">
          Lokasi Cigi Farm
        </h2>
        <p className="text-gray-300 mb-4">
          📍 Desa Cimande Girang, Kec. Caringin, Kab. Bogor
        </p>
        <iframe
          className="w-full h-64 rounded-xl"
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d126923.05799363125!2d106.73124176077644!3d-6.595038918405893!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x2e69c5eeb8b5b2e9%3A0x301e8f1fc28b9d0!2sBogor%2C%20West%20Java!5e0!3m2!1sen!2sid!4v1691136744746!5m2!1sen!2sid"
          allowFullScreen=""
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          title="Lokasi Cigi Farm"
        ></iframe>
      </div>

      {/* Kontak */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold mb-4 text-white">Kontak</h2>
        <p className="text-gray-300 mb-2">
          📞 WhatsApp:{" "}
          <a
            href="https://wa.me/6281234567890"
            className="text-blue-400 hover:underline"
          >
            +62 812-3456-7890
          </a>
        </p>
        <p className="text-gray-300">📧 Email: <EMAIL></p>
      </div>

      {/* CTA */}
      <div className="text-center mt-16">
        <p className="text-gray-400 mb-4">
          Tertarik bekerja sama atau ingin tahu lebih lanjut?
        </p>
        <a
          href="/contact"
          className="inline-block bg-green-600 hover:bg-green-700 text-white font-semibold px-6 py-2 rounded-lg transition"
        >
          Hubungi Kami
        </a>
      </div>
    </>
  );
};

export default CigiFarm;
