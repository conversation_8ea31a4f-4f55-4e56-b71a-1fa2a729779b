import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import Navbar from './components/Navbar.jsx'

import 'remixicon/fonts/remixicon.css'
import Footer from './components/Footer.jsx'
import PreLoader from './components/PreLoader.jsx'

import 'animate.css';
import AOS from 'aos';
import 'aos/dist/aos.css'; // You can also use <link> for styles
import { BrowserRouter, Route, Routes } from 'react-router-dom'
import About from './Pages/About.jsx'
import Contact from './Pages/Contact.jsx'
import Usaha from './Pages/Usaha.jsx'
import Cigifarm from './Pages/Detailusaha/Cigifarm.jsx'
import CigiNet from './Pages/Detailusaha/Ciginet.jsx'
// ..
AOS.init();

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <PreLoader/>
      <div className="container mx-auto px-4 min-h-screen">
        <Navbar/>
        <Routes>
          <Route path="/" element={<App />} />
          <Route path="/about" element={<About />} />
          <Route path="/usaha" element={<Usaha />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/Detailusaha/Cigifarm" element={<Cigifarm />} />
          <Route path="/Detailusaha/Ciginet" element={<CigiNet />} />
        </Routes>
        <Footer />
      </div>
    </BrowserRouter>
  </StrictMode>
)
