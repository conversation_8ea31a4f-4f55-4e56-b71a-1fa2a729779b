import React from "react";
import { Link } from "react-router-dom";

const unitUsahaData = [
  {
    nama: "Cigi Farm",
    gambar: "https://picsum.photos/300/200",
    deskripsi:
      "Peternakan ayam petelur, ikan lele, dan ikan nila untuk ketahanan pangan desa.",
    link: "/Detailusaha/Cigifarm",
  },
  {
    nama: "Cigi Net",
    gambar: "https://picsum.photos/300/200",
    deskripsi: "Layanan internet desa untuk konektivitas masyarakat Cimande Girang.",
    link: "/Detailusaha/Ciginet",
  },
  {
    nama: "Cigi Mart",
    gambar: "https://picsum.photos/300/200",
    deskripsi: "Supermarket lokal menyediakan sembako dan kebutuhan harian warga.",
  },
  {
    nama: "Cigi Food",
    gambar: "https://picsum.photos/300/200",
    deskripsi: "Produk makanan dan minuman olahan khas desa yang lezat dan higienis.",
  },
  {
    nama: "Cigi FC",
    gambar: "https://picsum.photos/300/200",
    deskripsi: "Klub sepak bola desa untuk pembinaan dan kegiatan olahraga pemuda.",
  },
  {
    nama: "Cigi Panahan",
    gambar: "https://picsum.photos/300/200",
    deskripsi: "Unit olahraga panahan untuk kegiatan positif dan tradisional desa.",
  },
  {
    nama: "Cigi Bulu Tangkis",
    gambar: "https://picsum.photos/300/200",
    deskripsi: "Klub bulu tangkis untuk anak-anak dan remaja desa.",
  },
  {
    nama: "Cigi Media",
    gambar: "https://picsum.photos/300/200",
    deskripsi: "Media dokumentasi dan promosi kegiatan UMKM dan warga.",
  },
];

export default function Usaha() {
  return (
    <div className="max-w-6xl mx-auto px-6 py-20 text-white">
      <h1 className="text-4xl font-bold text-center mb-12">Unit Usaha</h1>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
        {unitUsahaData.map((unit, index) => {
          const content = (
            <div className="bg-gray-800 rounded-xl overflow-hidden shadow-md hover:shadow-lg transition cursor-pointer">
              <img
                src={unit.gambar}
                alt={unit.nama}
                className="w-full h-48 object-cover"
              />
              <div className="p-5">
                <h2 className="text-xl font-semibold text-white mb-2">{unit.nama}</h2>
                <p className="text-gray-300 text-sm">{unit.deskripsi}</p>
              </div>
            </div>
          );

          // Jika punya link, bungkus dengan <Link>
          return unit.link ? (
            <Link to={unit.link} key={index} className="block">
              {content}
            </Link>
          ) : (
            <div key={index}>{content}</div>
          );
        })}
      </div>
    </div>
  );
}