import React from "react";
import {
  FaWifi,
  FaInfinity,
  FaUsers,
  FaCheckCircle,
  FaMoneyBillWave,
  FaHeadset,
} from "react-icons/fa";

const paketInternet = [
  {
    nama: "Paket Basic",
    kecepatan: "20 Mbps",
    logo: <FaWifi size={28} />,
    unlimited: true,
    harga: "Rp 120.000 / bulan",
    koneksi: "5 Perangkat",
  },
  {
    nama: "Paket Keluarga",
    kecepatan: "50 Mbps",
    logo: <FaWifi size={28} />,
    unlimited: true,
    harga: "Rp 180.000 / bulan",
    koneksi: "10 Perangkat",
  },
  {
    nama: "Paket Super",
    kecepatan: "100 Mbps",
    logo: <FaWifi size={28} />,
    unlimited: true,
    harga: "Rp 250.000 / bulan",
    koneksi: "20 Perangkat",
  },
];

export default function CigiNet() {
  return (
    <div className="text-white px-4 py-12 max-w-6xl mx-auto space-y-20">
      {/* Gambar dan Profil */}
      <div className="flex flex-col md:flex-row-reverse items-center gap-8">
        <img
          src="https://source.unsplash.com/600x400/?internet,wifi"
          alt="Cigi Net"
          className="w-full md:w-1/2 rounded-xl shadow-lg object-cover"
        />
        <div>
          <h2 className="text-2xl font-semibold mb-4">Tentang Cigi Net</h2>
          <p className="text-gray-300">
            <strong>Cigi Net</strong> adalah layanan penyedia internet milik
            masyarakat Desa Cimande Girang yang menghadirkan koneksi cepat,
            stabil, dan terjangkau untuk seluruh lapisan masyarakat. Dengan
            jaringan mandiri dan dukungan lokal, kami siap menyambungkan desa
            dengan dunia.
          </p>
        </div>
      </div>

      {/* Sejarah Singkat */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">Sejarah Singkat</h2>
        <p className="text-gray-300">
          Cigi Net didirikan pada tahun 2022 sebagai solusi atas keterbatasan
          akses internet di desa. Berawal dari jaringan kecil untuk RT, kini
          telah berkembang menjangkau seluruh desa bahkan hingga kampung
          tetangga.
        </p>
      </div>

      {/* Card Paket Internet */}
      <div>
        <h2 className="text-2xl font-semibold mb-8 text-center">
          Paket Internet Cigi Net
        </h2>
        <div className="flex flex-wrap justify-center gap-6">
          {paketInternet.map((paket, index) => (
            <div
              key={index}
              className="w-full sm:w-[300px] bg-gray-800 rounded-2xl shadow-lg overflow-hidden flex flex-col h-full"
            >
              {/* Header */}
              <div className="bg-purple-700 px-5 py-4 text-center">
                <h3 className="text-lg font-bold text-white mb-1">
                  {paket.nama}
                </h3>
              </div>

              {/* Content */}
              <div className="flex-1 flex flex-col items-center justify-center px-5 py-6 space-y-3">
                <div className="flex items-center gap-2">
                  {paket.logo}
                  <span className="text-lg font-semibold">
                    {paket.kecepatan}
                  </span>
                  {paket.unlimited && (
                    <FaInfinity
                      size={20}
                      className="text-green-400"
                      title="Unlimited"
                    />
                  )}
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-300">
                  <FaUsers />
                  {paket.koneksi}
                </div>
              </div>

              {/* Footer */}
              <div className="bg-gray-800 px-5 py-5 text-center rounded-b-2xl">
                <p className="text-xl text-white font-extrabold mb-3">
                  {paket.harga}
                </p>
                <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-semibold">
                  Pasang Sekarang
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Kenapa Harus Pilih */}
      <div>
        <h2 className="text-2xl font-semibold mb-6 text-center">
          Kenapa Harus Pilih CigiNet?
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center text-gray-300">
          <div className="bg-gray-800 p-6 rounded-xl shadow">
            <FaCheckCircle className="mx-auto text-green-400 text-3xl mb-2" />
            <h4 className="font-semibold text-lg mb-1">
              Internet Cepat & Stabil
            </h4>
            <p className="text-sm">
              Streaming, Zoom, dan Gaming tanpa gangguan.
            </p>
          </div>
          <div className="bg-gray-800 p-6 rounded-xl shadow">
            <FaMoneyBillWave className="mx-auto text-yellow-400 text-3xl mb-2" />
            <h4 className="font-semibold text-lg mb-1">
              Paket Murah Berkualitas
            </h4>
            <p className="text-sm">Harga bersahabat, kualitas tetap unggul.</p>
          </div>
          <div className="bg-gray-800 p-6 rounded-xl shadow">
            <FaHeadset className="mx-auto text-blue-400 text-3xl mb-2" />
            <h4 className="font-semibold text-lg mb-1">Layanan 24 Jam</h4>
            <p className="text-sm">
              Tim support lokal yang siap membantu kapan saja.
            </p>
          </div>
        </div>
      </div>

      {/* Maps */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">Lokasi Kami</h2>
        <iframe
          className="w-full h-64 rounded-xl shadow-md"
          src="https://maps.google.com/maps?q=Cimande%20Girang&t=&z=13&ie=UTF8&iwloc=&output=embed"
          loading="lazy"
        ></iframe>
      </div>

      {/* Kontak */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">Kontak</h2>
        <ul className="text-gray-300 space-y-2">
          <li>
            <strong>Alamat:</strong> Desa Cimande Girang, Kec. Caringin, Kab.
            Bogor
          </li>
          <li>
            <strong>Telp/WA:</strong> 0812-3456-7890
          </li>
          <li>
            <strong>Email:</strong> <EMAIL>
          </li>
        </ul>
      </div>
    </div>
  );
}
